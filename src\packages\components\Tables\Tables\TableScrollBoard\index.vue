<template>
  <div class="dv-scroll-board">
    <div
      class="header"
      v-if="status.header.length && status.mergedConfig"
      :style="`background-color: ${status.mergedConfig.headerBGC};`"
    >
      <div
        class="header-item"
        v-for="(headerItem, i) in status.header"
        :key="`${headerItem}${i}`"
        :style="`
        height: ${status.mergedConfig.headerHeight}px;
        line-height: ${status.mergedConfig.headerHeight}px;
        width: ${status.widths[i]}px;
      `"
        :align="status.aligns[i]"
        v-html="headerItem"
      />
    </div>

    <div
      v-if="status.mergedConfig"
      class="rows"
      :style="`height: ${h - (status.header.length ? status.mergedConfig.headerHeight : 0)}px;`"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @wheel="handleWheel"
    >
      <div
        class="row-item"
        v-for="(row, ri) in status.rows"
        :key="`${row.toString()}${row.scroll}`"
        :style="`
        height: ${status.heights[ri]}px;
        line-height: ${status.heights[ri]}px;
        background-color: ${status.mergedConfig[row.rowIndex % 2 === 0 ? 'evenRowBGC' : 'oddRowBGC']};
      `"
      >
        <div
          class="ceil"
          v-for="(ceil, ci) in row.ceils"
          :key="`${ceil}${ri}${ci}`"
          :style="`width: ${status.widths[ci]}px;`"
          :align="status.aligns[ci]"
          v-html="ceil"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, onUnmounted, reactive, toRefs, watch, onMounted } from 'vue'
import { CreateComponentType } from '@/packages/index.d'
import { useChartDataFetch } from '@/hooks'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import merge from 'lodash/merge'
import cloneDeep from 'lodash/cloneDeep'

const props = defineProps({
  chartConfig: {
    type: Object as PropType<CreateComponentType>,
    required: true
  }
})

// 这里能拿到图表宽高等
const { w, h } = toRefs(props.chartConfig.attr)
// 这里能拿到上面 config.ts 里的 option 数据
// const { rowNum, headerHeight, index, backgroundColor } = toRefs(props.chartConfig.option)

const status = reactive({
  defaultConfig: {
    header: [],
    dataset: [],
    rowNum: 5,
    headerBGC: '#00BAFF',
    oddRowBGC: '#003B51',
    evenRowBGC: '#0A2732',
    waitTime: 2,
    headerHeight: 35,
    columnWidth: [],
    align: [],
    index: false,
    indexHeader: '#',
    carousel: 'single',
    hoverPause: true
  },
  mergedConfig: props.chartConfig.option,
  header: [],
  rowsData: [],
  rows: [
    {
      ceils: [],
      rowIndex: 0,
      scroll: 0
    }
  ],
  widths: [],
  heights: [0],
  avgHeight: 0,
  aligns: [],
  animationIndex: 0,
  animationHandler: 0,
  updater: 0,
  needCalc: false
})

const calcData = () => {
  mergeConfig()
  calcHeaderData()
  calcRowsData()
  calcWidths()
  calcHeights()
  calcAligns()
  animation(true)
}

const handleMouseEnter = () => {
  const { hoverPause } = status.mergedConfig
  if (hoverPause) {
    stopAnimation() // 暂停滚动
  }
}

const handleMouseLeave = () => {
  const { hoverPause } = status.mergedConfig
  if (hoverPause) {
    status.updater = (status.updater + 1) % 999999
    animation(true) // 恢复滚动
  }
}

// 鼠标滚轮事件
const handleWheel = (e: WheelEvent) => {
  e.preventDefault() // 阻止默认滚动（如页面滚动）

  const { hoverPause, wheelScroll } = status.mergedConfig

  // 只有在启用滚轮控制且悬停暂停时才允许滚轮操作
  if (wheelScroll && hoverPause) {
    if (e.deltaY > 0) {
      // 向下滚动（滚轮向前）- 显示后面的数据
      scrollDown()
    } else {
      // 向上滚动（滚轮向后）- 显示前面的数据
      scrollUp()
    }
  }
}

const scrollUp = () => {
  const { rowsData, mergedConfig } = status
  const { rowNum } = mergedConfig

  // 检查是否有足够的数据和是否已经到顶部
  if (!rowsData || rowsData.length <= rowNum) return

  // 获取当前显示的第一行在原数据中的索引
  const currentFirstIndex = status.rows[0]?.scroll || 0

  // 如果已经到顶部，不能再向上滚动
  if (currentFirstIndex <= 0) return

  // 向上滚动一行，显示前一行数据
  const newStartIndex = Math.max(0, currentFirstIndex - 1)
  const newRows = rowsData.slice(newStartIndex, newStartIndex + rowNum + 1)

  status.rows = newRows.map((row: any, index: number) => ({
    ...row,
    scroll: newStartIndex + index
  }))
}

const scrollDown = () => {
  const { rowsData, mergedConfig } = status
  const { rowNum } = mergedConfig

  // 检查是否有足够的数据
  if (!rowsData || rowsData.length <= rowNum) return

  // 获取当前显示的第一行在原数据中的索引
  const currentFirstIndex = status.rows[0]?.scroll || 0

  // 如果已经到底部，不能再向下滚动
  if (currentFirstIndex >= rowsData.length - rowNum) return

  // 向下滚动一行，显示后一行数据
  const newStartIndex = Math.min(rowsData.length - rowNum, currentFirstIndex + 1)
  const newRows = rowsData.slice(newStartIndex, newStartIndex + rowNum + 1)

  status.rows = newRows.map((row: any, index: number) => ({
    ...row,
    scroll: newStartIndex + index
  }))
}

onMounted(() => {
  calcData()
})

const mergeConfig = () => {
  status.mergedConfig = merge(cloneDeep(status.defaultConfig), props.chartConfig.option)
}

const calcHeaderData = () => {
  let { header, index, indexHeader } = status.mergedConfig
  if (!header.length) {
    status.header = []
    return
  }
  header = [...header]
  if (index) header.unshift(indexHeader)
  status.header = header
}

const calcRowsData = () => {
  let { dataset, index, headerBGC, rowNum } = status.mergedConfig

  // 确保 dataset 是数组
  if (!Array.isArray(dataset)) {
    console.warn('TableScrollBoard: dataset is not an array, using empty array as fallback')
    dataset = []
  }

  if (index) {
    dataset = dataset.map((row: any, i: number) => {
      row = [...row]
      const indexTag = `<span class="index" style="background-color: ${headerBGC};border-radius: 3px;padding: 0px 3px;">${
        i + 1
      }</span>`
      row.unshift(indexTag)
      return row
    })
  }
  dataset = dataset.map((ceils: any, i: number) => ({ ceils, rowIndex: i }))
  const rowLength = dataset.length
  if (rowLength > rowNum && rowLength < 2 * rowNum) {
    dataset = [...dataset, ...dataset]
  }
  dataset = dataset.map((d: any, i: number) => ({ ...d, scroll: i }))

  status.rowsData = dataset
  status.rows = dataset
}

const calcWidths = () => {
  const { mergedConfig, rowsData } = status
  const { columnWidth, header } = mergedConfig
  const usedWidth = columnWidth.reduce((all: any, ws: number) => all + ws, 0)
  let columnNum = 0
  if (rowsData[0]) {
    columnNum = (rowsData[0] as any).ceils.length
  } else if (header.length) {
    columnNum = header.length
  }
  const avgWidth = (w.value - usedWidth) / (columnNum - columnWidth.length)
  const widths = new Array(columnNum).fill(avgWidth)
  status.widths = merge(widths, columnWidth)
}

const calcHeights = (onresize = false) => {
  const { mergedConfig, header } = status
  const { headerHeight, rowNum, dataset } = mergedConfig
  let allHeight = h.value
  if (header && header.length) allHeight -= headerHeight
  const avgHeight = allHeight / rowNum
  status.avgHeight = avgHeight
  // 确保 dataset 存在且是数组
  if (!onresize && dataset && Array.isArray(dataset)) {
    status.heights = new Array(dataset.length).fill(avgHeight)
  }
}

const calcAligns = () => {
  const { header, mergedConfig } = status

  const columnNum = header && header.length ? header.length : 0

  let aligns = new Array(columnNum).fill('left')

  const { align } = mergedConfig

  status.aligns = merge(aligns, align)
}

const animation = async (start = false) => {
  const { needCalc } = status

  if (needCalc) {
    calcRowsData()
    calcHeights()
    status.needCalc = false
  }
  let { avgHeight, animationIndex, mergedConfig, rowsData, updater } = status
  const { waitTime, carousel, rowNum } = mergedConfig

  const rowLength = rowsData.length
  if (rowNum >= rowLength) return
  if (start) {
    await new Promise(resolve => setTimeout(resolve, waitTime * 1000))
    if (updater !== status.updater) return
  }
  const animationNum = carousel === 'single' ? 1 : rowNum
  let rows = rowsData.slice(animationIndex)
  rows.push(...rowsData.slice(0, animationIndex))
  status.rows = rows.slice(0, carousel === 'page' ? rowNum * 2 : rowNum + 1)
  status.heights = new Array(rowLength).fill(avgHeight)
  await new Promise(resolve => setTimeout(resolve, 300))
  if (updater !== status.updater) return
  status.heights.splice(0, animationNum, ...new Array(animationNum).fill(0))
  animationIndex += animationNum
  const back = animationIndex - rowLength
  if (back >= 0) animationIndex = back
  status.animationIndex = animationIndex
  status.animationHandler = setTimeout(animation, waitTime * 1000 - 300) as any
}

const stopAnimation = () => {
  status.updater = (status.updater + 1) % 999999
  if (!status.animationHandler) return
  clearTimeout(status.animationHandler)
}

const onRestart = async () => {
  try {
    if (!status.mergedConfig) return
    stopAnimation()
    calcData()
  } catch (error) {
    console.log(error)
  }
}

watch(
  () => w.value,
  () => {
    onRestart()
  }
)

watch(
  () => h.value,
  () => {
    onRestart()
  }
)

// 数据更新
watch(
  () => props.chartConfig.option,
  () => {
    onRestart()
  },
  { deep: true }
)

const hoverStyle = document.createElement('style')
hoverStyle.innerHTML = `
  .dv-scroll-board .row-item:hover {
    background-color: ${status.mergedConfig.hoverColor} !important;
  }
`
document.head.appendChild(hoverStyle)

// 数据更新 (默认更新 dataset，若更新之后有其它操作，可添加回调函数)
useChartDataFetch(props.chartConfig, useChartEditStore, (resData: any[]) => {
  props.chartConfig.option.dataset = resData
  onRestart()
})

onUnmounted(() => {
  stopAnimation()
})
</script>

<style lang="scss" scoped>
.rows {
  overflow: hidden; /* 防止内容溢出 */
  cursor: grab;     /* 可选：提示用户可交互 */
  user-select: none; /* 禁止文本选中 */
}
.dv-scroll-board {
  position: relative;
  width: 100%;
  height: 100%;
  color: #fff;

  .text {
    padding: 0 10px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .header {
    display: flex;
    flex-direction: row;
    font-size: 15px;

    .header-item {
      transition: all 0.3s;
    }
  }

  .rows {
    overflow: hidden;

    .row-item {
      display: flex;
      font-size: 14px;
      transition: all 0.3s;
      overflow: hidden;
    }
  }
}
</style>
